<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>互动地图（分组版 BOSS）</title>
  <link rel="stylesheet" href="https://unpkg.com/leaflet/dist/leaflet.css">
  <style>
    body, html { margin: 0; padding: 0; height: 100%; display: flex; }
    #map { flex: 1; height: 100vh; background: black; }
    .leaflet-marker-icon.completed-icon { filter: grayscale(100%) opacity(0.5); }

    #sidebar {
      width: 250px;
      background: rgba(17,17,17,0.95);
      color: white;
      padding: 15px;
      overflow-y: auto;
      border-right: 2px solid #444;
      box-shadow: 2px 0 10px rgba(0,0,0,0.3);
    }

    .sidebar-title {
      font-size: 18px;
      font-weight: bold;
      margin-bottom: 15px;
      text-align: center;
      color: #fff;
      border-bottom: 2px solid #667eea;
      padding-bottom: 10px;
    }

    .icon-category {
      margin-bottom: 20px;
    }

    .category-title {
      font-size: 14px;
      font-weight: bold;
      margin-bottom: 10px;
      color: #667eea;
      text-transform: uppercase;
      letter-spacing: 1px;
    }

    .icon-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 8px;
    }

    .icon-item {
      background: #333;
      border: 2px solid #555;
      border-radius: 8px;
      padding: 8px;
      text-align: center;
      cursor: grab;
      transition: all 0.3s ease;
      user-select: none;
    }

    .icon-item:hover {
      background: #444;
      border-color: #667eea;
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
    }

    .icon-item:active {
      cursor: grabbing;
      transform: scale(0.95);
    }

    .icon-item img {
      width: 32px;
      height: 32px;
      display: block;
      margin: 0 auto 5px auto;
    }

    .icon-item .icon-name {
      font-size: 10px;
      color: #ccc;
      line-height: 1.2;
    }
    #panel {
      position: absolute;
      top: 10px;
      right: 10px;
      width: 180px;
      background: rgba(17,17,17,0.9);
      color: white;
      padding: 10px;
      display: flex;
      flex-direction: column;
      gap: 10px;
      z-index: 1000;
      border-radius: 5px;
    }

    .map-container {
      position: relative;
      flex: 1;
    }
    .action-btn {
      cursor: pointer;
      text-align: center;
      border: 1px solid #444;
      padding: 5px;
      border-radius: 5px;
      background: #222;
    }
    .action-btn:hover { background: #333; }

    .custom-popup {
      min-width: 280px;
      max-width: 400px;
      font-family: Arial, sans-serif;
    }
    .popup-header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 12px 15px;
      margin: -12px -15px 12px -15px;
      border-radius: 8px 8px 0 0;
      font-size: 16px;
      font-weight: bold;
      position: relative;
    }
    .popup-content {
      color: #333;
      line-height: 1.5;
      margin-bottom: 15px;
      font-size: 14px;
    }
    .popup-footer {
      text-align: right;
      border-top: 1px solid #eee;
      padding-top: 10px;
    }
    .edit-btn {
      background: #4CAF50;
      color: white;
      border: none;
      padding: 6px 12px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 12px;
      transition: background 0.3s;
    }
    .edit-btn:hover {
      background: #45a049;
    }
  </style>
</head>
<body>
  <div id="sidebar">
    <div class="sidebar-title">图标库</div>

    <div class="icon-category">
      <div class="category-title">BOSS</div>
      <div class="icon-grid" id="bossIcons">
        <!-- BOSS图标将在这里动态生成 -->
      </div>
    </div>

    <div class="icon-category">
      <div class="category-title">NPC</div>
      <div class="icon-grid" id="npcIcons">
        <!-- NPC图标将在这里动态生成 -->
      </div>
    </div>

    <div class="icon-category">
      <div class="category-title">道具</div>
      <div class="icon-grid" id="itemIcons">
        <!-- 道具图标将在这里动态生成 -->
      </div>
    </div>
  </div>

  <div class="map-container">
    <div id="map"></div>
    <div id="panel">
      <div class="action-btn" id="exportBtn">导出 JSON</div>
      <input type="file" id="importInput" accept=".json" style="display:none;">
      <div class="action-btn" id="importBtn">导入 JSON</div>
    </div>
  </div></body>

  <script src="https://unpkg.com/leaflet/dist/leaflet.js"></script>
  <script>
    const map = L.map('map', { crs: L.CRS.Simple, minZoom: -2 });
    const w = 4000, h = 3000;
    const bounds = [[0,0],[h,w]];
    L.imageOverlay('silksong_map.png', bounds).addTo(map);
    map.fitBounds(bounds);

    // 图标分类数据
    const ICON_CATEGORIES = {
      BOSS: [
        "苔藓之母",
        "丝母",
        "深林之王",
        "钢铁守卫",
        "暗影领主",
        "火焰巨龙"
      ],
      NPC: [
        "商人",
        "铁匠",
        "药剂师",
        "向导",
        "守卫",
        "村民"
      ],
      ITEM: [
        "宝箱",
        "钥匙",
        "药水",
        "武器",
        "护甲",
        "符文"
      ]
    };

    let selectedGroup = "BOSS";
    let selectedName = null;

    const iconData = [];

    // 初始化图标库
    function initIconLibrary() {
      Object.keys(ICON_CATEGORIES).forEach(category => {
        const containerName = category.toLowerCase() + 'Icons';
        const container = document.getElementById(containerName);

        ICON_CATEGORIES[category].forEach(iconName => {
          const iconItem = document.createElement('div');
          iconItem.className = 'icon-item';
          iconItem.draggable = true;
          iconItem.dataset.category = category;
          iconItem.dataset.name = iconName;

          iconItem.innerHTML = `
            <img src="icons/${category}/${iconName}.png" alt="${iconName}" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjMyIiBoZWlnaHQ9IjMyIiBmaWxsPSIjNjY3ZWVhIi8+Cjx0ZXh0IHg9IjE2IiB5PSIyMCIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjEwIiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+Pz88L3RleHQ+Cjwvc3ZnPg=='">
            <div class="icon-name">${iconName}</div>
          `;

          // 拖拽开始事件
          iconItem.addEventListener('dragstart', (e) => {
            e.dataTransfer.setData('text/plain', JSON.stringify({
              category: category,
              name: iconName
            }));
            iconItem.style.opacity = '0.5';
          });

          // 拖拽结束事件
          iconItem.addEventListener('dragend', (e) => {
            iconItem.style.opacity = '1';
          });

          container.appendChild(iconItem);
        });
      });
    }

    // 全局编辑函数
    function editIconInfo(index) {
      const data = iconData[index];
      if (!data) return;

      const newTitle = prompt("输入标题:", data.title || data.name);
      if (newTitle === null) return; // 用户取消

      const newDescription = prompt("输入描述:", data.description || data.content);
      if (newDescription === null) return; // 用户取消

      // 更新数据
      data.title = newTitle;
      data.description = newDescription;

      // 更新弹窗内容
      data.updatePopup();
    }

    function saveToJSON() {
      const data = iconData.map(d => ({
        group: d.group,
        name: d.name,
        x: d.x,
        y: d.y,
        content: d.content,
        title: d.title || "",
        description: d.description || "",
        completed: d.completed
      }));
      return JSON.stringify(data, null, 2);
    }

    function exportData() {
      const blob = new Blob([saveToJSON()], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = "map_data.json";
      a.click();
      URL.revokeObjectURL(url);
    }

    function importData(file) {
      const reader = new FileReader();
      reader.onload = e => {
        const data = JSON.parse(e.target.result);
        iconData.forEach(d => map.removeLayer(d.marker));
        iconData.length = 0;
        data.forEach(d => addIcon(d.x, d.y, d.group, d.name, d.content, d.completed, d.title, d.description));
      };
      reader.readAsText(file);
    }

    function addIcon(x, y, group, name, content="", completed=false, title="", description="") {
      const icon = L.icon({
        iconUrl: `icons/${group}/${name}.png`,
        iconSize: [48,48],
        iconAnchor: [24,24],
        popupAnchor: [0,-24]
      });
      const marker = L.marker([y, x], { icon, draggable: true }).addTo(map);

      if (completed) marker.getElement().classList.add('completed-icon');

      const data = { marker, group, name, x, y, content, completed, title, description };

      iconData.push(data);

      // 创建弹窗内容的函数
      data.createPopupContent = function() {
        const displayTitle = data.title || data.name;
        const displayDescription = data.description || data.content || "暂无描述";
        const index = iconData.indexOf(data);

        return `<div class="custom-popup">
          <div class="popup-header">${displayTitle}</div>
          <div class="popup-content">${displayDescription}</div>
          <div class="popup-footer">
            <button class="edit-btn" onclick="editIconInfo(${index}); event.stopPropagation();">✏️ 编辑</button>
          </div>
        </div>`;
      };

      // 设置初始弹窗内容
      marker.bindPopup(data.createPopupContent());

      marker.on('dragend', e => {
        const pos = e.target.getLatLng();
        data.x = pos.lng;
        data.y = pos.lat;
      });

      marker.on('dblclick', () => {
        data.completed = !data.completed;
        const el = marker.getElement();
        if (data.completed) el.classList.add('completed-icon');
        else el.classList.remove('completed-icon');
      });

      // 更新弹窗内容的函数
      data.updatePopup = function() {
        marker.setPopupContent(data.createPopupContent());
      };
    }

    // 地图拖拽放置功能
    const mapElement = document.getElementById('map');

    mapElement.addEventListener('dragover', (e) => {
      e.preventDefault();
    });

    mapElement.addEventListener('drop', (e) => {
      e.preventDefault();
      const data = JSON.parse(e.dataTransfer.getData('text/plain'));

      // 获取地图坐标
      const rect = mapElement.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;

      // 转换为地图坐标
      const latlng = map.containerPointToLatLng([x, y]);

      // 添加图标
      const content = prompt("图标内容（可选）：", "") || "";
      addIcon(latlng.lng, latlng.lat, data.category, data.name, content);
    });

    // 可选：保留点击地图添加图标的功能（用于快速添加）
    map.on('click', e => {
      if (e.originalEvent.ctrlKey) { // 按住Ctrl键点击才添加
        const categories = Object.keys(ICON_CATEGORIES);
        const categoryChoice = prompt("选择分类:\n" + categories.join("\n"));
        if (!categoryChoice || !categories.includes(categoryChoice)) return;

        const names = ICON_CATEGORIES[categoryChoice];
        const nameChoice = prompt("选择图标:\n" + names.join("\n"));
        if (!nameChoice || !names.includes(nameChoice)) return;

        const content = prompt("图标内容：", "");
        addIcon(e.latlng.lng, e.latlng.lat, categoryChoice, nameChoice, content);
      }
    });

    document.getElementById('exportBtn').addEventListener('click', exportData);
    document.getElementById('importBtn').addEventListener('click', () => {
      document.getElementById('importInput').click();
    });
    document.getElementById('importInput').addEventListener('change', e => {
      if (e.target.files.length > 0) importData(e.target.files[0]);
      e.target.value = '';
    });

    // 示例添加第一个 BOSS
    addIcon(2200, 2800, "BOSS", "苔藓之母", "苔藓之母 BOSS", false, "", "");
  </script>
</body>
</html>
