<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>互动地图（分组版 BOSS）</title>
  <link rel="stylesheet" href="https://unpkg.com/leaflet/dist/leaflet.css">
  <style>
    body, html { margin: 0; padding: 0; height: 100%; }
    #map { flex: 1; height: 100vh; width: 100%; background: black; }
    .leaflet-marker-icon.completed-icon { filter: grayscale(100%) opacity(0.5); }
    #panel {
      position: absolute;
      top: 10px;
      right: 10px;
      width: 180px;
      background: rgba(17,17,17,0.9);
      color: white;
      padding: 10px;
      display: flex;
      flex-direction: column;
      gap: 10px;
      z-index: 1000;
      border-radius: 5px;
    }
    .action-btn {
      cursor: pointer;
      text-align: center;
      border: 1px solid #444;
      padding: 5px;
      border-radius: 5px;
      background: #222;
    }
    .action-btn:hover { background: #333; }
  </style>
</head>
<body>
  <div id="map"></div>
  <div id="panel">
    <div class="action-btn" id="exportBtn">导出 JSON</div>
    <input type="file" id="importInput" accept=".json" style="display:none;">
    <div class="action-btn" id="importBtn">导入 JSON</div>
  </div>

  <script src="https://unpkg.com/leaflet/dist/leaflet.js"></script>
  <script>
    const map = L.map('map', { crs: L.CRS.Simple, minZoom: -2 });
    const w = 4000, h = 3000;
    const bounds = [[0,0],[h,w]];
    L.imageOverlay('silksong_map.png', bounds).addTo(map);
    map.fitBounds(bounds);

    // BOSS 分组素材
    const BOSS_LIST = [
      "苔藓之母",
      "丝母",
      "深林之王"
    ];

    let selectedGroup = "BOSS";
    let selectedName = null;

    const iconData = [];

    function saveToJSON() {
      const data = iconData.map(d => ({
        group: d.group,
        name: d.name,
        x: d.x,
        y: d.y,
        content: d.content,
        completed: d.completed
      }));
      return JSON.stringify(data, null, 2);
    }

    function exportData() {
      const blob = new Blob([saveToJSON()], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = "map_data.json";
      a.click();
      URL.revokeObjectURL(url);
    }

    function importData(file) {
      const reader = new FileReader();
      reader.onload = e => {
        const data = JSON.parse(e.target.result);
        iconData.forEach(d => map.removeLayer(d.marker));
        iconData.length = 0;
        data.forEach(d => addIcon(d.x, d.y, d.group, d.name, d.content, d.completed));
      };
      reader.readAsText(file);
    }

    function addIcon(x, y, group, name, content="", completed=false) {
      const icon = L.icon({
        iconUrl: `icons/${group}/${name}.png`,
        iconSize: [48,48],
        iconAnchor: [24,24],
        popupAnchor: [0,-24]
      });
      const marker = L.marker([y, x], { icon, draggable: true }).addTo(map);
      marker.bindPopup(content);
      if (completed) marker.getElement().classList.add('completed-icon');

      const data = { marker, group, name, x, y, content, completed };
      iconData.push(data);

      marker.on('dragend', e => {
        const pos = e.target.getLatLng();
        data.x = pos.lng;
        data.y = pos.lat;
      });

      marker.on('dblclick', () => {
        data.completed = !data.completed;
        const el = marker.getElement();
        if (data.completed) el.classList.add('completed-icon');
        else el.classList.remove('completed-icon');
      });
    }

    // 点击地图添加 BOSS 图标
    map.on('click', e => {
      // 弹出选择框选择 BOSS
      const name = prompt("选择 BOSS 名称:\n" + BOSS_LIST.join("\n"));
      if (!name || !BOSS_LIST.includes(name)) return alert("请选择有效名称！");
      const content = prompt("图标内容：", "");
      addIcon(e.latlng.lng, e.latlng.lat, selectedGroup, name, content);
    });

    document.getElementById('exportBtn').addEventListener('click', exportData);
    document.getElementById('importBtn').addEventListener('click', () => {
      document.getElementById('importInput').click();
    });
    document.getElementById('importInput').addEventListener('change', e => {
      if (e.target.files.length > 0) importData(e.target.files[0]);
      e.target.value = '';
    });

    // 示例添加第一个 BOSS
    addIcon(2200, 2800, "BOSS", "苔藓之母", "苔藓之母 BOSS");
  </script>
</body>
</html>
